/* BackgroundMusicModal 专用样式文件 */

.background-music-modal {
  /* 内容区域样式 */
  .drawer-content {
    padding: 20px;
    height: calc(100% - 120px);
    overflow-y: auto;
    min-height: 400px; /* 防止内容被过度压缩 */
    
    /* 确保在小屏幕下也有合理的显示 */
    @media (max-height: 600px) {
      min-height: 300px;
      height: calc(100% - 100px);
    }
  }

  /* 底部按钮区域样式 */
  .drawer-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 10px 16px;
    text-align: right;
    left: 0;
    background: #fff;
    border-radius: 0 0 4px 4px;
    z-index: 10; /* 确保底部按钮不被其他内容覆盖 */
    border-top: 1px solid #e8e8e8; /* 添加顶部边框分隔 */
  }

  /* 表单项容器 */
  .form-item {
    margin-bottom: 20px;
    
    .form-label {
      margin-bottom: 10px;
      font-weight: 500;
      color: #333;
    }
  }

  /* 音频播放器区域样式 */
  .audio-player-container {
    margin-bottom: 20px;
    
    .audio-player-wrapper {
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 20px;
      min-height: 60px; /* 防止播放器被压缩 */
      border: 1px solid #e8e8e8;
      background-color: #fafafa;
      transition: all 0.3s ease; /* 添加过渡效果 */
      
      /* 悬停效果 */
      &:hover {
        border-color: #d9d9d9;
        background-color: #f0f0f0;
      }
      
      /* 音频元素样式 */
      audio {
        width: 100%;
        min-height: 40px; /* 确保音频控件最小高度 */
        max-width: 100%; /* 防止溢出 */
        outline: none; /* 移除焦点轮廓 */
        
        /* 确保音频控件在不同浏览器下的一致性 */
        &::-webkit-media-controls-panel {
          background-color: transparent;
        }
        
        &::-webkit-media-controls-play-button,
        &::-webkit-media-controls-pause-button {
          background-color: #1890ff;
          border-radius: 50%;
        }
      }
    }
    
    /* 空状态样式 */
    .audio-placeholder {
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 20px;
      text-align: center;
      color: #999;
      background: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 60px; /* 确保占位区域有足够高度 */
      border: 1px solid #e8e8e8;
      font-size: 14px;
      
      /* 添加图标支持 */
      &::before {
        content: '🎵';
        margin-right: 8px;
        font-size: 16px;
      }
    }
  }

  /* 按钮组样式 */
  .button-group {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    flex-shrink: 0; /* 防止按钮组被压缩 */
    gap: 12px; /* 按钮间距 */
    
    /* 响应式处理 */
    @media (max-width: 480px) {
      flex-direction: column;
      gap: 8px;
      
      .ant-btn {
        width: 100%;
      }
    }
  }

  /* 加载状态覆盖层 */
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .ant-spin {
      .ant-spin-dot {
        font-size: 24px;
      }
    }
  }

  /* 输入框增强样式 */
  .music-name-input {
    .ant-input {
      border-radius: 4px;
      transition: all 0.3s ease;
      
      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
      
      &::placeholder {
        color: #bfbfbf;
        font-size: 13px;
      }
    }
  }

  /* 错误状态样式 */
  .error-state {
    .audio-player-wrapper,
    .audio-placeholder {
      border-color: #ff4d4f;
      background-color: #fff2f0;
    }
  }

  /* 成功状态样式 */
  .success-state {
    .audio-player-wrapper {
      border-color: #52c41a;
      background-color: #f6ffed;
    }
  }
}

/* 全局音频元素优化 */
audio {
  /* 确保音频控件在所有容器中都能正常显示 */
  min-width: 200px;
  max-width: 100%;
  height: auto;

  /* 防止音频控件被其他元素覆盖 */
  position: relative;
  z-index: 1;

  /* ✅ 额外的防御性样式 */
  box-sizing: border-box;
  display: block;

  /* 🔧 修复可能的压缩问题 */
  flex-shrink: 0; /* 防止在 flex 容器中被压缩 */
  min-height: 32px; /* 确保最小高度 */

  /* 🔧 确保在不同设备上的一致性 */
  @media (max-width: 480px) {
    min-width: 150px;
    font-size: 14px;
  }

  /* 🔧 处理特殊情况下的显示问题 */
  &:not([controls]) {
    display: none; /* 如果没有控件，则隐藏 */
  }

  /* 🔧 确保加载状态下的显示 */
  &[src=""], &:not([src]) {
    min-height: 40px;
    background-color: #f5f5f5;
    border: 1px dashed #d9d9d9;
  }
}

/* 针对 Ant Design Drawer 的特殊优化 */
.ant-drawer.background-music-modal {
  .ant-drawer-body {
    padding: 0; /* 移除默认内边距，使用自定义布局 */
    overflow: hidden; /* 防止内容溢出 */
  }
  
  .ant-drawer-header {
    border-bottom: 1px solid #e8e8e8;
    padding: 16px 24px;
    
    .ant-drawer-title {
      font-size: 16px;
      font-weight: 600;
    }
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .background-music-modal {
    .drawer-content {
      padding: 16px;
      height: calc(100% - 100px);
    }
    
    .drawer-footer {
      padding: 8px 16px;
    }
    
    .audio-player-container {
      .audio-player-wrapper,
      .audio-placeholder {
        padding: 12px;
        min-height: 50px;
      }
    }
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .background-music-modal {
    .audio-player-wrapper {
      border-width: 2px;
      border-color: #000;
    }

    .audio-placeholder {
      border-width: 2px;
      border-color: #666;
      background-color: #f0f0f0;
    }
  }
}

/* ✅ 额外的容器级防御性样式 */
.background-music-modal {
  /* 🔧 确保模态框内容不会被意外压缩 */
  .ant-drawer-body {
    min-height: 400px;
    position: relative;

    /* 防止内容溢出导致的布局问题 */
    overflow-x: hidden;
    overflow-y: auto;
  }

  /* 🔧 针对音频播放器的特殊保护 */
  .audio-player-wrapper {
    /* 确保容器有足够的空间 */
    min-width: 250px;
    width: 100%;

    /* 防止内容被压缩 */
    overflow: visible;

    /* 确保音频元素有足够的空间 */
    audio {
      width: 100% !important;
      min-width: 200px !important;
      height: auto !important;
      min-height: 40px !important;
    }
  }

  /* 🔧 处理极端情况下的显示问题 */
  &.compressed-mode {
    .drawer-content {
      min-height: 300px;
      padding: 16px;
    }

    .audio-player-wrapper {
      min-height: 50px;
      padding: 10px;
    }

    .button-group {
      flex-direction: column;
      gap: 8px;
    }
  }
}

/* 🔧 针对特定浏览器的兼容性修复 */
@supports not (flex-shrink: 0) {
  .background-music-modal {
    .audio-player-wrapper audio {
      /* IE 兼容性回退 */
      width: 100%;
      min-width: 200px;
      display: block;
    }
  }
}
